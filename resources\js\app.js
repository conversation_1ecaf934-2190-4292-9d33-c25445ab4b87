import './bootstrap';

// COZA STORE JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuButton = document.querySelector('.mobile-menu-button');
    const mobileMenu = document.querySelector('.mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Hero carousel functionality
    let currentSlide = 0;
    const slides = [
        {
            subtitle: "Men New-Season",
            title: "JACKETS & COATS",
            image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 600'><rect width='400' height='600' fill='%23334155'/><circle cx='200' cy='200' r='80' fill='%23475569'/><rect x='120' y='280' width='160' height='200' rx='20' fill='%231e293b'/><text x='50%' y='90%' text-anchor='middle' dy='.3em' font-family='Arial' font-size='16' fill='%23cbd5e1'>Navy Jacket Model</text></svg>"
        },
        {
            subtitle: "Women Collection",
            title: "ELEGANT DRESSES",
            image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 600'><rect width='400' height='600' fill='%23be185d'/><circle cx='200' cy='180' r='60' fill='%23ec4899'/><rect x='140' y='240' width='120' height='180' rx='15' fill='%239d174d'/><text x='50%' y='90%' text-anchor='middle' dy='.3em' font-family='Arial' font-size='16' fill='%23fce7f3'>Elegant Dress Model</text></svg>"
        },
        {
            subtitle: "Accessories",
            title: "PREMIUM BAGS",
            image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 600'><rect width='400' height='600' fill='%23059669'/><rect x='150' y='200' width='100' height='80' rx='10' fill='%2306b6d4'/><rect x='160' y='180' width='80' height='20' rx='5' fill='%230891b2'/><text x='50%' y='90%' text-anchor='middle' dy='.3em' font-family='Arial' font-size='16' fill='%23a7f3d0'>Premium Bag</text></svg>"
        }
    ];

    const heroImage = document.querySelector('.hero-image');
    const heroSubtitle = document.querySelector('.hero-subtitle');
    const heroTitle = document.querySelector('.hero-title');
    const indicators = document.querySelectorAll('.slide-indicator');
    const prevButton = document.querySelector('.prev-slide');
    const nextButton = document.querySelector('.next-slide');

    function updateSlide(index) {
        currentSlide = index;
        if (heroImage) heroImage.style.backgroundImage = `url('${slides[index].image}')`;
        if (heroSubtitle) heroSubtitle.textContent = slides[index].subtitle;
        if (heroTitle) heroTitle.textContent = slides[index].title;

        indicators.forEach((indicator, i) => {
            indicator.classList.toggle('bg-blue-600', i === index);
            indicator.classList.toggle('bg-gray-300', i !== index);
        });
    }

    if (prevButton) {
        prevButton.addEventListener('click', () => {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            updateSlide(currentSlide);
        });
    }

    if (nextButton) {
        nextButton.addEventListener('click', () => {
            currentSlide = (currentSlide + 1) % slides.length;
            updateSlide(currentSlide);
        });
    }

    // Auto-play carousel
    setInterval(() => {
        currentSlide = (currentSlide + 1) % slides.length;
        updateSlide(currentSlide);
    }, 5000);

    // Add click handlers for indicators
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            updateSlide(index);
        });
    });

    // Shopping cart functionality
    let cartCount = 0;
    const cartCountElement = document.querySelector('.cart-count');
    const wishlistCountElement = document.querySelector('.wishlist-count');

    // Add to cart simulation
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function() {
            cartCount++;
            if (cartCountElement) {
                cartCountElement.textContent = cartCount;
            }

            // Show notification
            showNotification('Item added to cart!', 'success');
        });
    });

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium transform translate-x-full transition-transform duration-300 ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Search functionality
    const searchButton = document.querySelector('.search-button');
    if (searchButton) {
        searchButton.addEventListener('click', function() {
            showNotification('Search functionality coming soon!', 'info');
        });
    }
});
