<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COZA STORE - Men's Fashion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Custom styles for COZA STORE */
        .hero-gradient {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
            transform: translateY(-1px);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Navigation hover effects */
        .nav-link {
            position: relative;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 50%;
            background-color: #3b82f6;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header Navigation -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-800">
                        COZA<span class="text-blue-600">STORE</span>
                    </h1>
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="#" class="nav-link text-gray-700 hover:text-blue-600 font-medium">Home</a>
                    <a href="#" class="nav-link text-gray-700 hover:text-blue-600 font-medium">Shop</a>
                    <div class="relative">
                        <a href="#" class="nav-link text-gray-700 hover:text-blue-600 font-medium flex items-center">
                            Features
                            <span class="ml-1 bg-red-500 text-white text-xs px-2 py-1 rounded-full">HOT</span>
                        </a>
                    </div>
                    <a href="#" class="nav-link text-gray-700 hover:text-blue-600 font-medium">Blog</a>
                    <a href="#" class="nav-link text-gray-700 hover:text-blue-600 font-medium">About</a>
                    <a href="#" class="nav-link text-gray-700 hover:text-blue-600 font-medium">Contact</a>
                </nav>

                <!-- Right Icons -->
                <div class="flex items-center space-x-4">
                    <button class="search-button text-gray-600 hover:text-blue-600 transition-colors duration-300">
                        <i class="fas fa-search text-lg"></i>
                    </button>
                    <button class="text-gray-600 hover:text-blue-600 relative transition-colors duration-300">
                        <i class="fas fa-shopping-cart text-lg"></i>
                        <span class="cart-count absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
                    </button>
                    <button class="text-gray-600 hover:text-blue-600 relative transition-colors duration-300">
                        <i class="fas fa-heart text-lg"></i>
                        <span class="wishlist-count absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
                    </button>
                </div>

                <!-- Mobile Menu Button -->
                <button class="mobile-menu-button md:hidden text-gray-600 hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>

            <!-- Mobile Menu -->
            <div class="mobile-menu hidden md:hidden bg-white border-t border-gray-200">
                <div class="px-4 py-2 space-y-2">
                    <a href="#" class="block py-2 text-gray-700 hover:text-blue-600 font-medium">Home</a>
                    <a href="#" class="block py-2 text-gray-700 hover:text-blue-600 font-medium">Shop</a>
                    <a href="#" class="block py-2 text-gray-700 hover:text-blue-600 font-medium">Features</a>
                    <a href="#" class="block py-2 text-gray-700 hover:text-blue-600 font-medium">Blog</a>
                    <a href="#" class="block py-2 text-gray-700 hover:text-blue-600 font-medium">About</a>
                    <a href="#" class="block py-2 text-gray-700 hover:text-blue-600 font-medium">Contact</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative h-screen hero-gradient overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0">
            <!-- Man in jacket image placeholder - you'll need to add the actual image -->
            <div class="hero-image absolute right-0 top-0 h-full w-3/5 bg-cover bg-center bg-no-repeat transition-all duration-500"
                 style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 400 600%22><rect width=%22400%22 height=%22600%22 fill=%22%23334155%22/><circle cx=%22200%22 cy=%22200%22 r=%2280%22 fill=%22%23475569%22/><rect x=%22120%22 y=%22280%22 width=%22160%22 height=%22200%22 rx=%2220%22 fill=%22%231e293b%22/><text x=%2250%25%22 y=%2290%25%22 text-anchor=%22middle%22 dy=%22.3em%22 font-family=%22Arial%22 font-size=%2216%22 fill=%22%23cbd5e1%22>Navy Jacket Model</text></svg>');">
                <!-- Overlay gradient for better text readability -->
                <div class="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-gray-50 opacity-80"></div>
            </div>
        </div>

        <!-- Content Overlay -->
        <div class="relative z-10 container mx-auto px-4 h-full flex items-center">
            <div class="max-w-lg animate-fadeInUp">
                <p class="hero-subtitle text-gray-600 text-lg mb-2 font-medium tracking-wide">Men New-Season</p>
                <h2 class="hero-title text-5xl md:text-6xl font-bold text-gray-800 mb-8 leading-tight text-shadow">
                    JACKETS & COATS
                </h2>
                <button class="add-to-cart btn-primary text-white px-8 py-3 rounded-full font-medium text-lg transition-all duration-300 transform hover:scale-105">
                    SHOP NOW
                </button>
            </div>
        </div>

        <!-- Navigation Arrows -->
        <button class="prev-slide absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 shadow-lg transition-all duration-300 hover:scale-110">
            <i class="fas fa-chevron-left text-gray-600"></i>
        </button>
        <button class="next-slide absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 shadow-lg transition-all duration-300 hover:scale-110">
            <i class="fas fa-chevron-right text-gray-600"></i>
        </button>

        <!-- Slide Indicators -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2">
            <div class="slide-indicator w-3 h-3 bg-blue-600 rounded-full cursor-pointer transition-all duration-300 hover:scale-125"></div>
            <div class="slide-indicator w-3 h-3 bg-gray-300 rounded-full cursor-pointer transition-all duration-300 hover:scale-125"></div>
            <div class="slide-indicator w-3 h-3 bg-gray-300 rounded-full cursor-pointer transition-all duration-300 hover:scale-125"></div>
        </div>
    </section>

    <!-- Additional Sections (Optional) -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h3 class="text-3xl font-bold text-gray-800 mb-4">Featured Categories</h3>
                <p class="text-gray-600">Discover our latest collections</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg p-8 text-center card-hover shadow-md">
                    <i class="fas fa-tshirt text-4xl text-blue-600 mb-4"></i>
                    <h4 class="text-xl font-semibold text-gray-800 mb-2">Men's Clothing</h4>
                    <p class="text-gray-600">Latest fashion trends</p>
                </div>
                <div class="bg-white rounded-lg p-8 text-center card-hover shadow-md">
                    <i class="fas fa-female text-4xl text-blue-600 mb-4"></i>
                    <h4 class="text-xl font-semibold text-gray-800 mb-2">Women's Fashion</h4>
                    <p class="text-gray-600">Elegant and stylish</p>
                </div>
                <div class="bg-white rounded-lg p-8 text-center card-hover shadow-md">
                    <i class="fas fa-shoe-prints text-4xl text-blue-600 mb-4"></i>
                    <h4 class="text-xl font-semibold text-gray-800 mb-2">Accessories</h4>
                    <p class="text-gray-600">Complete your look</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h4 class="text-2xl font-bold mb-4">COZA<span class="text-blue-400">STORE</span></h4>
                <p class="text-gray-400 mb-4">Your destination for premium fashion</p>
                <div class="flex justify-center space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-instagram"></i></a>
                    <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-youtube"></i></a>
                </div>
                <p class="text-gray-500 text-sm mt-4">&copy; 2024 COZA STORE. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // COZA STORE JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuButton = document.querySelector('.mobile-menu-button');
            const mobileMenu = document.querySelector('.mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Hero carousel functionality
            let currentSlide = 0;
            const slides = [
                {
                    subtitle: "Men New-Season",
                    title: "JACKETS & COATS",
                    image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 600'><rect width='400' height='600' fill='%23334155'/><circle cx='200' cy='200' r='80' fill='%23475569'/><rect x='120' y='280' width='160' height='200' rx='20' fill='%231e293b'/><text x='50%' y='90%' text-anchor='middle' dy='.3em' font-family='Arial' font-size='16' fill='%23cbd5e1'>Navy Jacket Model</text></svg>"
                },
                {
                    subtitle: "Women Collection",
                    title: "ELEGANT DRESSES",
                    image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 600'><rect width='400' height='600' fill='%23be185d'/><circle cx='200' cy='180' r='60' fill='%23ec4899'/><rect x='140' y='240' width='120' height='180' rx='15' fill='%239d174d'/><text x='50%' y='90%' text-anchor='middle' dy='.3em' font-family='Arial' font-size='16' fill='%23fce7f3'>Elegant Dress Model</text></svg>"
                },
                {
                    subtitle: "Accessories",
                    title: "PREMIUM BAGS",
                    image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 600'><rect width='400' height='600' fill='%23059669'/><rect x='150' y='200' width='100' height='80' rx='10' fill='%2306b6d4'/><rect x='160' y='180' width='80' height='20' rx='5' fill='%230891b2'/><text x='50%' y='90%' text-anchor='middle' dy='.3em' font-family='Arial' font-size='16' fill='%23a7f3d0'>Premium Bag</text></svg>"
                }
            ];

            const heroImage = document.querySelector('.hero-image');
            const heroSubtitle = document.querySelector('.hero-subtitle');
            const heroTitle = document.querySelector('.hero-title');
            const indicators = document.querySelectorAll('.slide-indicator');
            const prevButton = document.querySelector('.prev-slide');
            const nextButton = document.querySelector('.next-slide');

            function updateSlide(index) {
                currentSlide = index;
                if (heroImage) heroImage.style.backgroundImage = `url('${slides[index].image}')`;
                if (heroSubtitle) heroSubtitle.textContent = slides[index].subtitle;
                if (heroTitle) heroTitle.textContent = slides[index].title;

                indicators.forEach((indicator, i) => {
                    indicator.classList.toggle('bg-blue-600', i === index);
                    indicator.classList.toggle('bg-gray-300', i !== index);
                });
            }

            if (prevButton) {
                prevButton.addEventListener('click', () => {
                    currentSlide = (currentSlide - 1 + slides.length) % slides.length;
                    updateSlide(currentSlide);
                });
            }

            if (nextButton) {
                nextButton.addEventListener('click', () => {
                    currentSlide = (currentSlide + 1) % slides.length;
                    updateSlide(currentSlide);
                });
            }

            // Auto-play carousel
            setInterval(() => {
                currentSlide = (currentSlide + 1) % slides.length;
                updateSlide(currentSlide);
            }, 5000);

            // Add click handlers for indicators
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    updateSlide(index);
                });
            });

            // Shopping cart functionality
            let cartCount = 0;
            const cartCountElement = document.querySelector('.cart-count');

            // Add to cart simulation
            document.querySelectorAll('.add-to-cart').forEach(button => {
                button.addEventListener('click', function() {
                    cartCount++;
                    if (cartCountElement) {
                        cartCountElement.textContent = cartCount;
                    }

                    // Show notification
                    showNotification('Item added to cart!', 'success');
                });
            });

            // Notification system
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium transform translate-x-full transition-transform duration-300 ${
                    type === 'success' ? 'bg-green-500' :
                    type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                }`;
                notification.textContent = message;

                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Remove after 3 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }

            // Search functionality
            const searchButton = document.querySelector('.search-button');
            if (searchButton) {
                searchButton.addEventListener('click', function() {
                    showNotification('Search functionality coming soon!', 'info');
                });
            }
        });
    </script>
</body>
</html><?php /**PATH C:\Users\<USER>\Desktop\ibamba\resources\views/welcome.blade.php ENDPATH**/ ?>